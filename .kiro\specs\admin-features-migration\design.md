# 设计文档

## 概述

本设计文档描述了将admin-backend中的三个业务功能（报价quotes、公司companies、审批approvals）迁移到admin-dashboard的技术方案。迁移将保持现有的后端API不变，仅将前端界面和逻辑迁移到统一的admin-dashboard中。

## 架构

### 整体架构
```
admin-dashboard (前端)
    ↓ HTTP API 调用
admin-backend (后端API)
    ↓ 数据库操作
PostgreSQL 数据库
```

### 目录结构
```
src/
├── routes/
│   ├── quotes/                    # 报价功能路由
│   │   ├── quote-list/           # 报价列表页面
│   │   ├── quote-detail/         # 报价详情页面
│   │   └── quote-create/         # 创建报价页面
│   ├── companies/                # 公司功能路由
│   │   ├── company-list/         # 公司列表页面
│   │   ├── company-detail/       # 公司详情页面
│   │   └── company-create/       # 创建公司页面
│   └── approvals/                # 审批功能路由
│       ├── approval-list/        # 审批列表页面
│       └── approval-detail/      # 审批详情页面
├── hooks/
│   └── api/
│       ├── quotes.tsx            # 报价API hooks
│       ├── companies.tsx         # 公司API hooks
│       └── approvals.tsx         # 审批API hooks
├── components/
│   ├── quotes/                   # 报价相关组件
│   ├── companies/                # 公司相关组件
│   └── approvals/                # 审批相关组件
└── i18n/
    └── translations/
        ├── zhCN.json            # 中文翻译
        └── en.json              # 英文翻译
```

## 组件和接口

### 路由组件设计

#### 1. 报价功能路由
- **QuoteList**: 报价列表页面，使用SingleColumnPage布局
- **QuoteDetail**: 报价详情页面，使用TwoColumnPage布局
- **QuoteCreate**: 创建报价页面（如需要）

#### 2. 公司功能路由
- **CompanyList**: 公司列表页面，使用SingleColumnPage布局
- **CompanyDetail**: 公司详情页面，使用TwoColumnPage布局
- **CompanyCreate**: 创建公司页面

#### 3. 审批功能路由
- **ApprovalList**: 审批列表页面，使用SingleColumnPage布局
- **ApprovalDetail**: 审批详情页面，使用TwoColumnPage布局

### API Hooks设计

#### 报价API Hooks
```typescript
// src/hooks/api/quotes.tsx
export const useQuotes = (query?: QuoteFilterParams) => { ... }
export const useQuote = (id: string, query?: QuoteFilterParams) => { ... }
export const useSendQuote = (id: string) => { ... }
export const useRejectQuote = (id: string) => { ... }
export const useCreateQuoteMessage = (id: string) => { ... }
```

#### 公司API Hooks
```typescript
// src/hooks/api/companies.tsx
export const useCompanies = (query?: Record<string, any>) => { ... }
export const useCompany = (companyId: string, query?: Record<string, any>) => { ... }
export const useCreateCompany = () => { ... }
export const useUpdateCompany = (companyId: string) => { ... }
export const useDeleteCompany = (companyId: string) => { ... }
```

#### 审批API Hooks
```typescript
// src/hooks/api/approvals.tsx
export const useApprovals = (query?: Record<string, any>) => { ... }
export const useUpdateApproval = (approvalId: string) => { ... }
export const useUpdateApprovalSettings = (companyId: string) => { ... }
```

### 组件设计

#### 表格组件
- **QuotesTable**: 报价列表表格，支持筛选、排序、分页
- **CompaniesTable**: 公司列表表格，支持搜索、筛选
- **ApprovalsTable**: 审批列表表格，支持状态筛选

#### 详情组件
- **QuoteDetailsSection**: 报价详情展示
- **QuoteItemsSection**: 报价商品列表
- **QuoteMessagesSection**: 报价消息记录
- **CompanyGeneralSection**: 公司基本信息
- **CompanyEmployeesSection**: 公司员工列表
- **ApprovalDetailsSection**: 审批详情展示

#### 表单组件
- **CompanyCreateForm**: 创建公司表单
- **CompanyEditForm**: 编辑公司表单
- **QuoteMessageForm**: 报价消息表单

## 数据模型

### 报价数据模型
```typescript
interface Quote {
  id: string
  customer: Customer
  company: Company
  status: 'pending_merchant' | 'pending_customer' | 'merchant_rejected' | 'customer_rejected' | 'accepted'
  amount: number
  currency_code: string
  created_at: string
  items: QuoteItem[]
  messages: QuoteMessage[]
}
```

### 公司数据模型
```typescript
interface Company {
  id: string
  name: string
  email: string
  phone: string
  address: string
  city: string
  state: string
  zip: string
  logo_url?: string
  employees: Employee[]
  customer_group?: CustomerGroup
  approval_settings?: ApprovalSettings
}
```

### 审批数据模型
```typescript
interface Approval {
  id: string
  company_id: string
  status: 'pending' | 'approved' | 'rejected'
  type: string
  entity_id: string
  created_at: string
  updated_at: string
}
```

## 错误处理

### API错误处理
- 使用统一的错误处理机制，通过FetchError类型处理API错误
- 在组件中显示用户友好的错误消息
- 支持重试机制和错误恢复

### 表单验证
- 使用react-hook-form进行表单验证
- 提供实时验证反馈
- 支持服务端验证错误显示

### 加载状态
- 使用骨架屏显示加载状态
- 支持局部加载状态显示
- 提供加载失败的重试选项

## 测试策略

### 单元测试
- 对所有API hooks进行单元测试
- 对核心业务逻辑组件进行测试
- 使用Mock Service Worker模拟API响应

### 集成测试
- 测试页面路由和导航
- 测试表单提交和数据更新流程
- 测试错误处理和边界情况

### 端到端测试
- 测试完整的用户工作流程
- 测试跨页面的数据一致性
- 测试响应式设计和用户体验

## 国际化设计

### 翻译文件结构
```json
{
  "quotes": {
    "title": "报价",
    "table": {
      "id": "报价号",
      "customer": "客户",
      "status": "状态",
      "company": "公司",
      "amount": "金额",
      "createdAt": "创建时间"
    },
    "status": {
      "pending_merchant": "等待商家处理",
      "pending_customer": "等待客户回复",
      "accepted": "已接受"
    }
  },
  "companies": {
    "title": "公司",
    "table": {
      "name": "名称",
      "phone": "电话",
      "email": "邮箱",
      "employees": "员工数"
    }
  },
  "approvals": {
    "title": "审批",
    "status": {
      "pending": "待审批",
      "approved": "已批准",
      "rejected": "已拒绝"
    }
  }
}
```

### 翻译键命名规范
- 使用层级结构组织翻译键
- 遵循admin-dashboard现有的命名约定
- 支持参数化翻译和复数形式

## 性能优化

### 数据获取优化
- 使用React Query进行数据缓存和同步
- 实现分页和虚拟滚动
- 支持数据预加载和后台更新

### 组件优化
- 使用React.memo优化组件渲染
- 实现代码分割和懒加载
- 优化表格组件的渲染性能

### 网络优化
- 实现请求去重和缓存
- 支持离线模式和数据同步
- 优化API请求的批处理

## 安全考虑

### 权限控制
- 继承admin-dashboard的权限系统
- 实现功能级别的权限控制
- 支持角色基础的访问控制

### 数据验证
- 前端表单验证
- 后端API数据验证
- 防止XSS和CSRF攻击

### 敏感数据处理
- 安全处理客户和公司信息
- 实现数据脱敏和访问日志
- 遵循数据保护法规要求