# 需求文档

## 介绍

本功能涉及将三个业务功能（报价quotes、公司companies、审批approvals）从admin-backend项目迁移到admin-dashboard项目。目标是将所有前端功能整合到admin-dashboard中，同时保持admin-backend作为纯API后端。这次迁移将通过分离关注点和提供统一的管理界面来改善架构。

## 需求

### 需求 1

**用户故事：** 作为管理员用户，我希望通过admin-dashboard界面管理报价，以便获得统一的管理体验。

#### 验收标准

1. 当我访问admin-dashboard时，系统应在导航中显示报价管理部分
2. 当我查看报价列表时，系统应显示包含报价号、客户、状态、公司、金额、创建时间的表格
3. 当我点击报价时，系统应显示报价详情页面，包含报价摘要、客户信息、商品列表、总计信息
4. 当我发送报价时，系统应通过后端API发送报价并更新状态
5. 当我拒绝报价时，系统应通过后端API更新状态为已拒绝
6. 当我查看报价消息时，系统应显示与该报价相关的所有消息记录
7. 当我管理报价商品时，系统应支持添加、编辑、删除商品项目
8. 如果后端API不可用，系统应显示适当的错误消息

### 需求 2

**用户故事：** 作为管理员用户，我希望通过admin-dashboard界面管理公司，以便在一个地方维护业务关系。

#### 验收标准

1. 当我访问公司部分时，系统应显示包含公司logo、名称、电话、邮箱、地址、员工数、客户组的表格
2. 当我创建新公司时，系统应提供表单输入公司基本信息并提交到后端API
3. 当我点击公司行时，系统应导航到公司详情页面
4. 当我编辑公司信息时，系统应通过后端API更新公司数据
5. 当我删除公司时，系统应通过后端API删除公司并刷新列表
6. 当我管理公司客户组时，系统应支持添加和移除公司的客户组关联
7. 当我查看公司员工时，系统应显示该公司的所有员工信息
8. 如果公司操作失败，系统应提供清晰的错误反馈

### 需求 3

**用户故事：** 作为管理员用户，我希望通过admin-dashboard界面处理审批，以便高效管理审批工作流。

#### 验收标准

1. 当我访问审批部分时，系统应显示所有审批记录的表格
2. 当我查看审批详情时，系统应显示审批的具体内容和状态
3. 当我批准项目时，系统应通过后端API更新审批状态为已批准
4. 当我拒绝项目时，系统应通过后端API更新审批状态为已拒绝
5. 当我查看审批历史时，系统应显示按时间顺序的审批记录
6. 当我管理公司审批设置时，系统应支持配置公司的审批规则
7. 如果审批操作失败，系统应保持数据一致性并显示错误

### 需求 4

**用户故事：** 作为开发者，我希望前端代码能够正确集成到现有的admin-dashboard架构中，以便代码库保持可维护性。

#### 验收标准

1. 当迁移组件时，系统应遵循现有的admin-dashboard模式和约定
2. 当添加新路由时，系统应与现有路由结构集成
3. 当实现API调用时，系统应使用一致的HTTP客户端模式
4. 当添加翻译时，系统应扩展现有的i18n结构
5. 当样式化组件时，系统应使用现有的设计系统和CSS模式
6. 如果需要新依赖，系统应最小化添加并证明需求合理

### 需求 5

**用户故事：** 作为系统管理员，我希望后端API保持功能完整且不变，以便迁移不会破坏现有集成。

#### 验收标准

1. 当前端迁移时，后端API端点应保持不变
2. 当发出API请求时，后端应继续以相同的数据结构响应
3. 当需要身份验证时，系统应使用现有的身份验证机制
4. 当发生错误时，后端应返回一致的错误响应
5. 如果后端需要修改，更改应是最小的且向后兼容
6. 当迁移完成时，所有现有API功能应保持完整