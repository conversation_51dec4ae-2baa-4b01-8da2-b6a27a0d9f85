import { useQueryParams } from "../../../../../hooks/use-query-params"

type UseCompaniesTableQueryProps = {
  pageSize: number
  prefix?: string
}

export const useCompaniesTableQuery = ({
  pageSize,
  prefix,
}: UseCompaniesTableQueryProps) => {
  const queryObject = useQueryParams(
    ["q"],
    prefix
  )

  const { q } = queryObject

  const searchParams = {
    q,
    fields: "*employees,*customer_group",
  }

  return {
    searchParams,
    raw: queryObject,
  }
}
